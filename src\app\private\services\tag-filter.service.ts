import { Injectable, inject } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
// Store imports removed as not used in this service
import { HybridDbService } from '../../shared/hybrid-db.service';
import { Customer } from '../../service/data/customer';
import { Category } from '../../service/data/category';
import { 
  CustomerTagFilter, 
  FilterConfiguration, 
  CategoryFilterResult, 
  TagFilterState,
  DatacolCategoryTag 
} from '../model/sector-hierarchy-response';
import { CatalogsService } from './catalog.service';

@Injectable({
  providedIn: 'root'
})
export class TagFilterService {
  private _dbService = inject(HybridDbService);
  private _catalogsService = inject(CatalogsService);

  // State management
  private _filterState = new BehaviorSubject<TagFilterState>({
    activeCustomerUid: null,
    currentFilter: null,
    availableConfigurations: [],
    isFilteringEnabled: false,
    lastFilterUpdate: 0
  });

  public filterState$ = this._filterState.asObservable();

  constructor() {
    this.initializeService();
  }

  /**
   * Inizializza il servizio e carica le configurazioni salvate
   */
  private async initializeService(): Promise<void> {
    try {
      console.log('🔄 Inizializzazione TagFilterService...');
      // Carica le configurazioni salvate dal database
      await this.loadSavedConfigurations();
      console.log('✅ TagFilterService inizializzato con successo');
    } catch (error) {
      console.error('❌ Errore nell\'inizializzazione del TagFilterService:', error);
    }
  }

  /**
   * Imposta il cliente attivo e carica i suoi filtri
   */
  async setActiveCustomer(customerUid: string): Promise<void> {
    try {
      console.log(`🔄 Impostazione cliente attivo: ${customerUid}`);
      
      // Carica i dati del cliente
      const customer = await this.getCustomerData(customerUid);
      if (!customer) {
        throw new Error(`Cliente ${customerUid} non trovato`);
      }

      // Crea il filtro predefinito basato sui dati del cliente
      const defaultFilter = await this.createDefaultFilter(customer);
      
      // Carica le configurazioni salvate per questo cliente
      const savedConfigurations = await this.loadCustomerConfigurations(customerUid);
      
      // Aggiorna lo stato
      const newState: TagFilterState = {
        activeCustomerUid: customerUid,
        currentFilter: defaultFilter,
        availableConfigurations: savedConfigurations,
        isFilteringEnabled: true,
        lastFilterUpdate: Date.now()
      };

      this._filterState.next(newState);
      console.log('✅ Cliente attivo impostato con successo');
      
    } catch (error) {
      console.error('❌ Errore nell\'impostazione del cliente attivo:', error);
      throw error;
    }
  }

  /**
   * Crea il filtro predefinito basato sui dati del cliente
   */
  private async createDefaultFilter(customer: Customer): Promise<CustomerTagFilter> {
    const filter: CustomerTagFilter = {
      customerUid: customer.uid,
      settore: customer.industrialSector || undefined,
      attivita: customer.codiceAttivita || undefined,
      professione: customer.codiceProfessione || undefined,
      universale: true, // Il tag universale è sempre incluso
      customTags: [],
      isDefault: true
    };

    console.log('📋 Filtro predefinito creato:', filter);
    return filter;
  }

  /**
   * Ottiene i dati del cliente dal database
   */
  private async getCustomerData(customerUid: string): Promise<Customer | null> {
    try {
      const customers = await this._dbService.getRecordsByANDCondition(
        'customers',
        [{ key: 'uid', value: customerUid }]
      ) as Customer[];
      
      return customers.length > 0 ? customers[0] : null;
    } catch (error) {
      console.error('❌ Errore nel recupero dati cliente:', error);
      return null;
    }
  }

  /**
   * Carica le configurazioni salvate per un cliente
   */
  private async loadCustomerConfigurations(customerUid: string): Promise<FilterConfiguration[]> {
    try {
      const records = await this._dbService.getRecordsByANDCondition(
        'filter_configurations',
        [{ key: 'customerUid', value: customerUid }]
      );

      return records.map(record => ({
        id: record.id,
        customerUid: record.customerUid,
        name: record.name,
        activeTags: JSON.parse(record.activeTags || '[]'),
        isDefault: record.isDefault === 1,
        createdAt: record.createdAt,
        lastUsed: record.lastUsed
      }));
    } catch (error) {
      console.error('❌ Errore nel caricamento configurazioni cliente:', error);
      return [];
    }
  }

  /**
   * Carica tutte le configurazioni salvate
   */
  private async loadSavedConfigurations(): Promise<void> {
    try {
      const records = await this._dbService.getAll(['filter_configurations']);
      console.log(`📂 Caricate ${records.length} configurazioni dal database`);
    } catch (error) {
      console.error('❌ Errore nel caricamento configurazioni:', error);
    }
  }

  /**
   * Salva una configurazione di filtri nel database
   */
  async saveFilterConfiguration(configuration: FilterConfiguration): Promise<void> {
    try {
      const record = {
        id: configuration.id,
        customerUid: configuration.customerUid,
        name: configuration.name,
        activeTags: JSON.stringify(configuration.activeTags),
        isDefault: configuration.isDefault ? 1 : 0,
        createdAt: configuration.createdAt,
        lastUsed: configuration.lastUsed
      };

      await this._dbService.addRecord(
        'filter_configurations',
        Object.keys(record),
        Object.values(record).map(v => String(v))
      );

      console.log(`✅ Configurazione ${configuration.name} salvata per cliente ${configuration.customerUid}`);
    } catch (error) {
      console.error('❌ Errore nel salvataggio configurazione:', error);
      throw error;
    }
  }

  /**
   * Aggiorna una configurazione esistente
   */
  async updateFilterConfiguration(configuration: FilterConfiguration): Promise<void> {
    try {
      const record = {
        customerUid: configuration.customerUid,
        name: configuration.name,
        activeTags: JSON.stringify(configuration.activeTags),
        isDefault: configuration.isDefault ? 1 : 0,
        createdAt: configuration.createdAt,
        lastUsed: Date.now() // Aggiorna il timestamp di ultimo utilizzo
      };

      const updateColumns = Object.keys(record).map(key => ({
        key: key,
        value: String(record[key])
      }));

      await this._dbService.updateRecord(
        'filter_configurations',
        [{ key: 'id', value: configuration.id }],
        updateColumns
      );

      console.log(`✅ Configurazione ${configuration.name} aggiornata`);
    } catch (error) {
      console.error('❌ Errore nell\'aggiornamento configurazione:', error);
      throw error;
    }
  }

  /**
   * Elimina una configurazione
   */
  async deleteFilterConfiguration(configurationId: string): Promise<void> {
    try {
      await this._dbService.deleteRecord(
        'filter_configurations',
        [{ key: 'id', value: configurationId }]
      );

      console.log(`✅ Configurazione ${configurationId} eliminata`);
    } catch (error) {
      console.error('❌ Errore nell\'eliminazione configurazione:', error);
      throw error;
    }
  }

  /**
   * Salva lo stato del filtro per un cliente
   */
  async saveCustomerFilterState(customerUid: string, filter: CustomerTagFilter): Promise<void> {
    try {
      const record = {
        customerUid: customerUid,
        currentFilterId: null, // Per ora non gestiamo ID specifici
        isFilteringEnabled: filter ? 1 : 0,
        lastFilterUpdate: Date.now(),
        settore: filter?.settore || null,
        attivita: filter?.attivita || null,
        professione: filter?.professione || null,
        customTags: JSON.stringify(filter?.customTags || [])
      };

      // Verifica se esiste già un record per questo cliente
      const existingRecords = await this._dbService.getRecordsByANDCondition(
        'customer_filter_state',
        [{ key: 'customerUid', value: customerUid }]
      );

      if (existingRecords.length > 0) {
        // Aggiorna il record esistente
        const updateColumns = Object.keys(record).map(key => ({
          key: key,
          value: String(record[key])
        }));

        await this._dbService.updateRecord(
          'customer_filter_state',
          [{ key: 'customerUid', value: customerUid }],
          updateColumns
        );
      } else {
        // Crea un nuovo record
        await this._dbService.addRecord(
          'customer_filter_state',
          Object.keys(record),
          Object.values(record)
        );
      }

      console.log(`✅ Stato filtro salvato per cliente ${customerUid}`);
    } catch (error) {
      console.error('❌ Errore nel salvataggio stato filtro:', error);
      throw error;
    }
  }

  /**
   * Carica lo stato del filtro per un cliente
   */
  async loadCustomerFilterState(customerUid: string): Promise<CustomerTagFilter | null> {
    try {
      const records = await this._dbService.getRecordsByANDCondition(
        'customer_filter_state',
        [{ key: 'customerUid', value: customerUid }]
      );

      if (records.length === 0) {
        return null;
      }

      const record = records[0];
      return {
        customerUid: record.customerUid,
        settore: record.settore,
        attivita: record.attivita,
        professione: record.professione,
        universale: true, // Sempre abilitato
        customTags: JSON.parse(record.customTags || '[]'),
        isDefault: !record.settore && !record.attivita && !record.professione &&
                   JSON.parse(record.customTags || '[]').length === 0
      };
    } catch (error) {
      console.error('❌ Errore nel caricamento stato filtro:', error);
      return null;
    }
  }

  /**
   * Applica i filtri alle categorie e restituisce il risultato
   */
  async applyFiltersToCategories(categories: Category[]): Promise<CategoryFilterResult> {
    const currentState = this._filterState.value;
    
    if (!currentState.isFilteringEnabled || !currentState.currentFilter) {
      // Se il filtraggio non è abilitato, mostra tutte le categorie
      return {
        visibleCategories: categories.map(c => c.id),
        hiddenCategories: [],
        totalVisible: categories.length,
        totalHidden: 0
      };
    }

    try {
      const visibleCategories: string[] = [];
      const hiddenCategories: string[] = [];

      // Filtra le DatacolCategory (quelle con isProduct = true)
      for (const category of categories) {
        if (category.isProduct) {
          const isVisible = await this.isCategoryVisible(category, currentState.currentFilter);
          if (isVisible) {
            visibleCategories.push(category.id);
          } else {
            hiddenCategories.push(category.id);
          }
        } else {
          // Per le categorie non-prodotto, verifica se hanno figli visibili
          const hasVisibleChildren = await this.hasVisibleChildren(category, categories, currentState.currentFilter);
          if (hasVisibleChildren) {
            visibleCategories.push(category.id);
          } else {
            hiddenCategories.push(category.id);
          }
        }
      }

      return {
        visibleCategories,
        hiddenCategories,
        totalVisible: visibleCategories.length,
        totalHidden: hiddenCategories.length
      };

    } catch (error) {
      console.error('❌ Errore nell\'applicazione dei filtri:', error);
      // In caso di errore, mostra tutte le categorie
      return {
        visibleCategories: categories.map(c => c.id),
        hiddenCategories: [],
        totalVisible: categories.length,
        totalHidden: 0
      };
    }
  }

  /**
   * Verifica se una categoria è visibile in base ai filtri
   */
  private async isCategoryVisible(category: Category, filter: CustomerTagFilter): Promise<boolean> {
    try {
      // Ottieni i tag assegnati alla categoria
      const categoryTags = await this.getCategoryTags(category);
      
      if (categoryTags.length === 0) {
        // Se la categoria non ha tag, non è visibile
        return false;
      }

      // Crea l'array dei tag attivi per il filtro
      const activeTags = this.getActiveTagsFromFilter(filter);
      
      // Verifica se almeno uno dei tag della categoria è presente nei tag attivi
      return categoryTags.some(tag => activeTags.includes(tag.keyTag));
      
    } catch (error) {
      console.error('❌ Errore nella verifica visibilità categoria:', error);
      return false;
    }
  }

  /**
   * Ottiene i tag assegnati a una categoria (con cache locale)
   */
  private async getCategoryTags(category: Category): Promise<DatacolCategoryTag[]> {
    try {
      if (!category.idSubCategory || !category.idCatalog) {
        return [];
      }

      const idSubCategory = parseInt(category.idSubCategory);
      const idCatalog = category.idCatalog;

      // Prima controlla la cache locale
      const cachedTags = await this.getCachedCategoryTags(idSubCategory, idCatalog);
      if (cachedTags.length > 0) {
        return cachedTags;
      }

      // Se non ci sono tag in cache, recupera dal server
      return new Promise((resolve) => {
        this._catalogsService.getDatacolCategoryTags({
          idSubCategory: idSubCategory,
          idCatalog: idCatalog
        }).subscribe({
          next: async (response) => {
            if (response.status === 'OK' && response.content?.tags) {
              // Salva i tag in cache
              await this.cacheCategoryTags(idSubCategory, idCatalog, response.content.tags);
              resolve(response.content.tags);
            } else {
              resolve([]);
            }
          },
          error: (error) => {
            console.error('❌ Errore nel recupero tag categoria:', error);
            resolve([]);
          }
        });
      });
    } catch (error) {
      console.error('❌ Errore nel recupero tag categoria:', error);
      return [];
    }
  }

  /**
   * Recupera i tag di una categoria dalla cache locale
   */
  private async getCachedCategoryTags(idSubCategory: number, idCatalog: number): Promise<DatacolCategoryTag[]> {
    try {
      const records = await this._dbService.getRecordsByANDCondition(
        'datacol_category_tags',
        [
          { key: 'idSubCategory', value: String(idSubCategory) },
          { key: 'idCatalog', value: String(idCatalog) }
        ]
      );

      return records.map(record => ({
        keyTag: record.keyTag,
        description: record.description
      }));
    } catch (error) {
      console.error('❌ Errore nel recupero tag dalla cache:', error);
      return [];
    }
  }

  /**
   * Salva i tag di una categoria nella cache locale
   */
  private async cacheCategoryTags(idSubCategory: number, idCatalog: number, tags: DatacolCategoryTag[]): Promise<void> {
    try {
      // Prima elimina i tag esistenti per questa categoria
      await this._dbService.deleteRecord(
        'datacol_category_tags',
        [
          { key: 'idSubCategory', value: String(idSubCategory) },
          { key: 'idCatalog', value: String(idCatalog) }
        ]
      );

      // Poi inserisce i nuovi tag
      const timestamp = Date.now();
      for (const tag of tags) {
        await this._dbService.addRecord(
          'datacol_category_tags',
          ['idSubCategory', 'idCatalog', 'keyTag', 'description', 'lastSync'],
          [String(idSubCategory), String(idCatalog), tag.keyTag, tag.description || '', String(timestamp)]
        );
      }

      console.log(`✅ Cached ${tags.length} tags for category ${idSubCategory}`);
    } catch (error) {
      console.error('❌ Errore nel salvataggio tag in cache:', error);
    }
  }

  /**
   * Pulisce la cache dei tag più vecchi di un certo timestamp
   */
  async cleanupTagCache(olderThanTimestamp: number): Promise<void> {
    try {
      // Nota: questo richiederebbe un metodo personalizzato per query complesse
      // Per ora logghiamo l'operazione
      console.log(`🧹 Cleanup cache tag più vecchi di ${olderThanTimestamp}`);

      // In futuro si potrebbe implementare una query personalizzata:
      // DELETE FROM datacol_category_tags WHERE lastSync < olderThanTimestamp
    } catch (error) {
      console.error('❌ Errore nel cleanup cache tag:', error);
    }
  }

  /**
   * Estrae i tag attivi dal filtro
   */
  private getActiveTagsFromFilter(filter: CustomerTagFilter): string[] {
    const activeTags: string[] = [];
    
    // Aggiungi i tag predefiniti del cliente
    if (filter.settore) activeTags.push(filter.settore);
    if (filter.attivita) activeTags.push(filter.attivita);
    if (filter.professione) activeTags.push(filter.professione);
    if (filter.universale) activeTags.push('UNIVERSALE'); // Assumendo che il tag universale abbia questa chiave
    
    // Aggiungi i tag personalizzati
    activeTags.push(...filter.customTags);
    
    return activeTags;
  }

  /**
   * Verifica se una categoria ha figli visibili
   */
  private async hasVisibleChildren(
    parentCategory: Category, 
    allCategories: Category[], 
    filter: CustomerTagFilter
  ): Promise<boolean> {
    try {
      // Trova le categorie figlie
      const children = allCategories.filter(cat => 
        cat.idParent === parentCategory.id || 
        (cat.idApp && parentCategory.idApp && cat.idApp.startsWith(parentCategory.idApp) && cat.id !== parentCategory.id)
      );

      // Verifica ricorsivamente se almeno un figlio è visibile
      for (const child of children) {
        if (child.isProduct) {
          const isVisible = await this.isCategoryVisible(child, filter);
          if (isVisible) return true;
        } else {
          const hasVisibleGrandchildren = await this.hasVisibleChildren(child, allCategories, filter);
          if (hasVisibleGrandchildren) return true;
        }
      }

      return false;
    } catch (error) {
      console.error('❌ Errore nella verifica figli visibili:', error);
      return false;
    }
  }

  /**
   * Aggiunge un tag personalizzato al filtro corrente
   */
  async addCustomTag(keyTag: string): Promise<void> {
    const currentState = this._filterState.value;
    
    if (!currentState.currentFilter) {
      throw new Error('Nessun filtro attivo');
    }

    // Verifica che il tag non sia già presente
    if (currentState.currentFilter.customTags.includes(keyTag)) {
      console.warn(`Tag ${keyTag} già presente nei filtri personalizzati`);
      return;
    }

    // Aggiunge il tag ai filtri personalizzati
    const updatedFilter: CustomerTagFilter = {
      ...currentState.currentFilter,
      customTags: [...currentState.currentFilter.customTags, keyTag],
      isDefault: false // Non è più la configurazione predefinita
    };

    const newState: TagFilterState = {
      ...currentState,
      currentFilter: updatedFilter,
      lastFilterUpdate: Date.now()
    };

    this._filterState.next(newState);
    console.log(`✅ Tag personalizzato ${keyTag} aggiunto`);
  }

  /**
   * Rimuove un tag personalizzato dal filtro corrente
   */
  async removeCustomTag(keyTag: string): Promise<void> {
    const currentState = this._filterState.value;
    
    if (!currentState.currentFilter) {
      throw new Error('Nessun filtro attivo');
    }

    // Verifica che non sia un tag predefinito (non rimovibile)
    const predefinedTags = [
      currentState.currentFilter.settore,
      currentState.currentFilter.attivita,
      currentState.currentFilter.professione,
      'UNIVERSALE'
    ].filter(Boolean);

    if (predefinedTags.includes(keyTag)) {
      throw new Error(`Il tag ${keyTag} è predefinito e non può essere rimosso`);
    }

    // Rimuove il tag dai filtri personalizzati
    const updatedFilter: CustomerTagFilter = {
      ...currentState.currentFilter,
      customTags: currentState.currentFilter.customTags.filter(tag => tag !== keyTag)
    };

    const newState: TagFilterState = {
      ...currentState,
      currentFilter: updatedFilter,
      lastFilterUpdate: Date.now()
    };

    this._filterState.next(newState);
    console.log(`✅ Tag personalizzato ${keyTag} rimosso`);
  }

  /**
   * Ottiene lo stato corrente del filtro
   */
  getCurrentFilter(): CustomerTagFilter | null {
    return this._filterState.value.currentFilter;
  }

  /**
   * Verifica se il filtraggio è abilitato
   */
  isFilteringEnabled(): boolean {
    return this._filterState.value.isFilteringEnabled;
  }

  /**
   * Abilita o disabilita il filtraggio
   */
  setFilteringEnabled(enabled: boolean): void {
    const currentState = this._filterState.value;
    const newState: TagFilterState = {
      ...currentState,
      isFilteringEnabled: enabled,
      lastFilterUpdate: Date.now()
    };

    this._filterState.next(newState);
    console.log(`🔄 Filtraggio ${enabled ? 'abilitato' : 'disabilitato'}`);
  }
}
